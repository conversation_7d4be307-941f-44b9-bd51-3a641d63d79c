<?php
// Exit if accessed directly.
defined('ABSPATH') || exit;

add_action('wp_ajax_get_users_list_voorodak', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('دسترسی غیرمجاز');
    }
    $users = get_users(['fields' => ['ID', 'user_login']]);
    $bom = "\xEF\xBB\xBF";
    $data = $bom . "نام کاربری,نام و نام خانوادگی,شماره تلفن\n";

    foreach ($users as $user) {
        $first_name = get_user_meta($user->ID, 'first_name', true);
        $last_name = get_user_meta($user->ID, 'last_name', true);
        $billing_phone = get_user_meta($user->ID, 'billing_phone', true);
        $data .= "{$user->user_login}," . ($first_name ? $first_name . ' ' : '') . $last_name . ",{$billing_phone}\n";
    }

    wp_send_json_success($data);
});


function add_lock_voorodak_meta_box() {
    global $post;
    if (empty($post) || !isset($post->ID)) {
        return;
    }
    $post_id = $post->ID;
    $voorodak_options = get_option(VOORODAK_OPTION);
    $illegals = [];
    if($voorodak_options && $voorodak_options['login_page_id'] != ''){
        $illegals[] = $voorodak_options['login_page_id'];
    }
    if (function_exists('is_woocommerce')){
        $illegals[] = wc_get_page_id('myaccount');
        $illegals[] = wc_get_page_id('checkout');
    }
    if (in_array($post_id, $illegals)) {
        return;
    }
    add_meta_box(
        'lock_voorodak_meta_box',
        'ورودک',
        'render_lock_voorodak_meta_box',
        ['post', 'page', 'product'],
        'side',
        'default'
    );
}

function render_lock_voorodak_meta_box($post) {
    $value = get_post_meta($post->ID, '_lock_voorodak', true);
    wp_nonce_field('save_lock_voorodak_meta_box', 'lock_voorodak_nonce');
    ?>
    <label for="lock_voorodak" style="margin-top: 10px;display: inline-block;">
        <input type="checkbox" name="lock_voorodak" id="lock_voorodak" value="1" <?php checked($value, '1'); ?> />
        <b>قفل کردن صفحه</b>
    </label>
    <div style="font-size: 12px;margin-top: 5px">با فعالسازی این گزینه، کاربران برای مشاهده این صفحه ابتدا باید در سایت ورود کنند، سپس به این صفحه بازمیگردن</div>
    <?php
}

function save_lock_voorodak_meta_box($post_id) {
    if (!isset($_POST['lock_voorodak_nonce']) || !wp_verify_nonce($_POST['lock_voorodak_nonce'], 'save_lock_voorodak_meta_box')) {
        return;
    }

    if (isset($_POST['lock_voorodak'])) {
        update_post_meta($post_id, '_lock_voorodak', '1');
    } else {
        delete_post_meta($post_id, '_lock_voorodak');
    }
}

add_action('add_meta_boxes', 'add_lock_voorodak_meta_box');
add_action('save_post', 'save_lock_voorodak_meta_box');

function voorodak_banned_field($user) {
    $banned = get_user_meta($user->ID, 'voorodak_banned', true);
    ?>
    <h2>تنظیمات ورودک</h2>
    <table class="form-table">
        <tr>
            <th><label for="voorodak_banned">مسدود کردن کاربر</label></th>
            <td>
                <input type="checkbox" name="voorodak_banned" id="voorodak_banned" value="1" <?php checked($banned, 1); ?>>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'voorodak_banned_field');
add_action('edit_user_profile', 'voorodak_banned_field');

function voorodak_save_banned_field($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return;
    }
    $banned = isset($_POST['voorodak_banned']) ? 1 : 0;
    update_user_meta($user_id, 'voorodak_banned', $banned);
}
add_action('personal_options_update', 'voorodak_save_banned_field');
add_action('edit_user_profile_update', 'voorodak_save_banned_field');


add_filter('voorodak_pre_do_login', function($data, $user_id) {
    $banned = get_user_meta($user_id, 'voorodak_banned', true);
    if ($banned){
        return [
            'allow' => false,
            'message' => 'این حساب کاربری توسط مدیریت مسدود شده و امکان ورود ندارد.'
        ];
    }
    return $data;
}, 10, 2);


function voorodak_log_display() {
    $logs = get_option('voorodak_log', []);

    if (empty($logs)) {
        echo '<p>هیچ لاگی ثبت نشده است.</p>';
        return;
    }

    echo '<ul style="list-style:none; padding:0;margin: 0; font-family: monospace;">';
    foreach ($logs as $log) {
        $line = str_replace(["\r", "\n"], ' ', $log);
        echo '<li style="border-bottom:1px solid #ccc; padding:6px 0;">' . esc_html($line) . '</li>';
    }
    echo '</ul>';
}
